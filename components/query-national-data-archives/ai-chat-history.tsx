import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import styles from "./ai-chat-history.module.css";

// 字段选项配置
const FIELD_OPTIONS = [
  { id: 'name', label: 'name', description: '国家名称' },
  { id: 'region', label: 'region', description: '所属大洲' },
  { id: 'capital', label: 'capital', description: '首都' },
  { id: 'area', label: 'area', description: '面积（平方公里）' },
  // { id: 'population', label: 'population', description: '人口数量' },
  { id: 'flags', label: 'flags', description: '国旗图片链接' },
];

// 自定义字段选择器组件
function FieldSelector({ 
  value, 
  onChange, 
  onKeyPress 
}: { 
  value: string; 
  onChange: (value: string) => void; 
  onKeyPress: (e: React.KeyboardEvent) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // 解析输入值为字段数组
  useEffect(() => {
    if (value) {
      const fields = value.split(',').map(f => f.trim()).filter(f => f);
      setSelectedFields(fields);
    } else {
      setSelectedFields([]);
    }
  }, [value]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 切换字段选择
  const toggleField = (fieldId: string) => {
    let newFields;
    if (selectedFields.includes(fieldId)) {
      newFields = selectedFields.filter(f => f !== fieldId);
    } else {
      newFields = [...selectedFields, fieldId];
    }
    
    const newValue = newFields.join(',');
    onChange(newValue);
  };

  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <div ref={containerRef} className="relative">
      {/* 输入框 */}
      <input
        type="text"
        value={value}
        onChange={handleInputChange}
        onKeyPress={onKeyPress}
        onFocus={() => setIsOpen(true)}
        placeholder="请选择或输入字段名"
        className="border-0 outline-none text-[#666666] placeholder-[#666666]"
        style={{
          width: '300px',
          height: '26px',
          background: '#F6FAFF',
          borderRadius: '6px',
          padding: '0 8px',
          fontSize: '14px'
        }}
      />

      {/* 下拉框 */}
      {isOpen && (
        <div 
          className="absolute top-full left-0 mt-1 bg-[#F6FAFF] border border-gray-200 rounded-lg shadow-lg z-50"
          style={{
            width: '300px',
            height: '60px',
            maxHeight: '200px',
            overflow: 'auto'
          }}
        >
          {/*/!* 当前输入值显示 *!/*/}
          {/*<div className="px-3 py-2 border-b border-gray-100">*/}
          {/*  <div className="text-sm text-gray-600 mb-1">当前选择：</div>*/}
          {/*  <div className="text-sm text-gray-800">*/}
          {/*    {value || 'name,region'}*/}
          {/*  </div>*/}
          {/*</div>*/}

          {/* 字段名标签 */}
          <div className="px-2 py-1 ">
            <div 
              className="text-[12px] text-[#666666] mb-[6px] font-normal leading-[18px] text-left"
              style={{
                fontFamily: 'FZLTHK--GBK1, FZLTHK--GBK1',
                fontStyle: 'normal'
              }}
            >
              字段名
            </div>
            <div className="flex flex-wrap gap-2">
              {FIELD_OPTIONS.map((option) => (
                <button
                  key={option.id}
                  onClick={() => toggleField(option.id)}
                  title={option.description}
                  className={`px-[6px] py-0.5 rounded-[4px] text-[12px] font-normal leading-[18px] text-left transition-colors cursor-pointer ${
                    selectedFields.includes(option.id)
                      ? 'bg-[#5282B7] text-white'
                      : 'bg-[#DEEDFF] text-[#5282B7] hover:bg-gray-200'
                  }`}
                  style={{
                    fontFamily: 'FZLTHK--GBK1, FZLTHK--GBK1',
                    fontStyle: 'normal'

                  }}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isCode?: boolean;
  language?: string;
}

interface AiChatHistoryProps {
  className?: string;
}



// API 输入栏组件
function ApiInputBar({ onSendMessage, className }: { onSendMessage?: (message: string) => void; className?: string }) {
  const [codeInput, setCodeInput] = useState('');
  const [fieldsInput, setFieldsInput] = useState('');

  const handleSend = () => {
    // 验证输入
    if (!codeInput.trim()) {
      return;
    }

    if (!fieldsInput.trim()) {
      return;
    }

    const fullMessage = `${codeInput}\n${fieldsInput}`;
    const fullMessageSend = `https://restcountries.com/v3.1/alpha/${codeInput}?fields=${fieldsInput}`;

    if (onSendMessage) {
      // 需要传递两个参数，但当前 onSendMessage 只接受一个参数
      // 可以传递一个包含两个信息的对象
      onSendMessage(JSON.stringify({
        displayMessage: fullMessage,
        apiUrl: fullMessageSend
      }));
      // 清空输入框
      setCodeInput('');
      setFieldsInput('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSend();
    }
  };


  // 添加点赞/点踩状态
  const [messageReactions, setMessageReactions] = useState<{[key: string]: 'like' | 'dislike' | null}>({});

// 处理点赞
  const handleLike = (messageId: string) => {
    setMessageReactions(prev => ({
      ...prev,
      [messageId]: prev[messageId] === 'like' ? null : 'like'
    }));
  };

// 处理点踩
  const handleDislike = (messageId: string) => {
    setMessageReactions(prev => ({
      ...prev,
      [messageId]: prev[messageId] === 'dislike' ? null : 'dislike'
    }));
  };



  return (
    <div 
      className={cn(
        "relative pl-4 pr-4 pt-6 ",
        className
      )}
      style={{
        width: '1024px',
        height: '152px',
        background: '#DEEDFF',
        borderRadius: '12px'
      }}
    >
      {/* 第一行：国家代码 */}
      <div className="flex items-center gap-0 mb-[6px]">
        <span 
          style={{

            fontWeight: 'normal',
            fontSize: '16px',
            color: '#5282B7',
            alignItems: 'center',
            lineHeight: '26px',
            fontStyle: 'normal',
            fontFamily: 'FZLTHK--GBK1, FZLTHK--GBK1',
          }}
        >
          国家代码：
        </span>

        <input
          type="text"
          value={codeInput}
          onChange={(e) => setCodeInput(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="输入代码"
          className="border-0 outline-none text-[#666666] placeholder-[#666666]"
          style={{
            width: '300px',
            height: '26px',
            background: '#F6FAFF',
            borderRadius: '6px',
            padding: '0 8px',
            fontSize: '14px',
            marginLeft: '6px',
          }}
        />
      </div>

      {/* 第二行：查询字段 */}
      <div className="flex items-center gap-0">
        <span 
          style={{
            fontFamily: 'FZLTHK--GBK1, FZLTHK--GBK1',
            fontWeight: 'normal',
            fontSize: '16px',
            color: '#5282B7',
            lineHeight: '26px',
            textAlign: 'left',
            fontStyle: 'normal'
          }}
        >
          查询字段：
        </span>

        <div style={{ marginLeft: '6px' }}>
          <FieldSelector
            value={fieldsInput}
            onChange={setFieldsInput}
            onKeyPress={handleKeyPress}
          />
        </div>
      </div>

      {/* 发送按钮 */}
      <button
        onClick={handleSend}
        className={`absolute bottom-6 right-4 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 shadow-md hover:shadow-lg ${
            (!codeInput.trim() || !fieldsInput.trim())
                ? " "
                : "cursor-pointer hover:cursor-pointer"
        }`}
        style={{
          width: '36px',
          height: '36px'
        }}
      >
        <img
          src="/ailab/explore-ready-made-data/icon_发送@2x.png"
          alt="发送"
          className="w-9 h-9"
          style={{
            width: '36px',
            height: '36px',
            opacity: (!codeInput.trim() || !fieldsInput.trim()) ? 0.3 : 1
          }}
        />
      </button>
    </div>
  );
}

export function AiChatHistory({
  className
}: AiChatHistoryProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: '请将你想要查询的内容，填写在文本输入框中，看看可以得到哪些信息。',
      timestamp: new Date()
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [messageReactions, setMessageReactions] = useState<Record<string, 'like' | 'dislike' | undefined>>({});

  // 新增：本地数据管理状态
  const [countryDataInfo, setCountryDataInfo] = useState<any[]>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [dataError, setDataError] = useState(false);

  // 自动滚动到底部
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      setTimeout(() => {
        scrollAreaRef.current!.scrollTop = scrollAreaRef.current!.scrollHeight;
      }, 0);
    }
  };

  useEffect(() => {
    // 使用 setTimeout 确保 DOM 更新后再滚动
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 100);
    
    return () => clearTimeout(timer);
  }, [messages]);

  // 处理 API 输入栏发送的消息
  const handleApiMessage = async (messageData: string) => {
    let displayMessage: string;
    let apiUrl: string;
    
    try {
      // 尝试解析 JSON 格式的消息数据
      const parsed = JSON.parse(messageData);
      displayMessage = parsed.displayMessage;
      apiUrl = parsed.apiUrl;
    } catch {
      // 如果解析失败，说明是旧格式，直接使用
      displayMessage = messageData;
      apiUrl = messageData;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: displayMessage, // 使用格式化的显示消息
      timestamp: new Date(),
      isCode: true,
    };

    setMessages(prev => [...prev, userMessage]);
    setTimeout(() => scrollToBottom(), 100);
    setIsLoading(true);

    try {
      // 使用实际的 API URL 进行请求
      const aiResponse = await analyzeApiRequest(apiUrl);
      
      const isJsonResponse = aiResponse.trim().startsWith('{') || aiResponse.trim().startsWith('[');

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date(),
        isCode: isJsonResponse,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '抱歉，分析 API 请求时遇到了问题。',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 分析 API 请求的函数
  const analyzeApiRequest = async (apiUrl: string): Promise<string> => {
    try {
      // 实际调用 REST Countries API
      const response = await fetch(apiUrl);

      if (!response.ok) {
        return '请输入正确的国家及地区代码和字段名，才能查询结果';
      }

      const data = await response.json();

      // 检查数据是否为空
      if (!data || Object.keys(data).length === 0) {
        return '请输入正确的国家及地区代码和字段名，才能查询结果';
      }

      // 直接返回原始数据的 JSON 字符串（无格式化，和浏览器一样）
      return JSON.stringify(data);

    } catch (error) {
      return '请输入正确的国家及地区代码和字段名，才能查询结果';
    }
  };



  // 复制消息内容
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const handleLike = (messageId: string) => {
    setMessageReactions(prev => ({
      ...prev,
      [messageId]: prev[messageId] === 'like' ? undefined : 'like'
    }));
  };

  const handleDislike = (messageId: string) => {
    setMessageReactions(prev => ({
      ...prev,
      [messageId]: prev[messageId] === 'dislike' ? undefined : 'dislike'
    }));
  };

  return (
    <div className={cn(
      "bg-[#F3F8FF]"
    )}>
      {/* 消息历史 */}
      <div 
        ref={scrollAreaRef}
        className="h-[396px]  pb-4 overflow-auto [&::-webkit-scrollbar]:hidden"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={cn(
              "flex",
              message.type === 'user' ? 'justify-end' : 'justify-start gap-3'
            )}>
              {/* AI 头像 */}
              {message.type === 'ai' && (
                <div className="w-9 h-9  rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
                  <img src="/ailab/explore-ready-made-data/ai-bot-icon.png" alt="AI" className={"w-9 h-9"}/>
                </div>
              )}

              {/* 消息气泡 */}
              <div className={cn(
                message.type === 'user'
                  ? "max-w-[544px] min-h-[48px] py-3 px-4 text-[#5282B7] text-base font-normal break-words whitespace-pre-wrap"
                  : "max-w-[896px] mt-[6px] rounded-lg text-[#5282B7] text-base font-normal break-words whitespace-pre-wrap"
              )}
              style={message.type === 'user' ? {
                background: '#C9E2FF',
                borderRadius: '12px 12px 2px 12px',
                fontFamily: 'FZLTHK--GBK1, FZLTHK--GBK1',
                textAlign: 'left',
                lineHeight: '1.5',
                wordBreak: 'break-all'

              } : undefined}>
                    {message.content}
                {/*/!* AI 消息的操作按钮 *!/*/}
                {/*{message.type === 'ai' && message.id !== "1"&& (*/}
                {/*    <div className="flex items-center gap-1 mt-0 pt-2">*/}

                {/*      <Button*/}
                {/*          variant="ghost"*/}
                {/*          size="sm"*/}
                {/*          onClick={() => handleCopy(message.content)}*/}
                {/*          // className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 cursor-pointer  "*/}
                {/*          className={styles.actionButton}*/}
                {/*      >*/}
                {/*        <img src="/ailab/explore-ready-made-data/icon_复制@2x.png" alt="复制" className="" />*/}
                {/*      </Button>*/}

                {/*      <Button*/}
                {/*          variant="ghost"*/}
                {/*          size="sm"*/}
                {/*          onClick={() => handleDislike(message.id)}*/}
                {/*          // className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 ml-2 mr-2 cursor-pointer"*/}
                {/*          className={styles.actionButton}*/}
                {/*      >*/}
                {/*        <img */}
                {/*          src={messageReactions[message.id] === 'dislike' ? "/ailab/explore-ready-made-data/icon_不喜欢**********" : "/ailab/explore-ready-made-data/icon_不喜欢@2x.png"}*/}
                {/*          alt="点踩" */}
                {/*          // className="w-6 h-6"*/}
                {/*        />*/}
                {/*      </Button>*/}

                {/*      <Button*/}
                {/*          variant="ghost"*/}
                {/*          size="sm"*/}
                {/*          onClick={() => handleLike(message.id)}*/}
                {/*          // className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 cursor-pointer"*/}
                {/*          className={styles.actionButton}*/}

                {/*      >*/}
                {/*        <img */}
                {/*          src={messageReactions[message.id] === 'like' ? "/ailab/explore-ready-made-data/icon_喜欢**********" : "/ailab/explore-ready-made-data/icon_喜欢@2x.png"}*/}
                {/*          alt="点赞" */}
                {/*          // className="w-6 h-6"*/}
                {/*        />*/}
                {/*      </Button>*/}
                {/*    </div>*/}
                {/*)}*/}


              </div>
            </div>
          ))}



        </div>
      </div>

      {/* 替换为新的 API 输入栏 */}
      <div className="  border-blue-200 flex justify-center">
        <ApiInputBar onSendMessage={handleApiMessage} />
      </div>
    </div>
  );
}